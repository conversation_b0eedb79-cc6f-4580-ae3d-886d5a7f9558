const Test = require('../../models/Test');
const Result = require('../../models/Result');   // optional but recommended
const asyncHandler = require('express-async-handler');
const { v4: uuid } = require('uuid');               // for a unique attemptId
const dayjs = require('dayjs');
const mongoose = require('mongoose');
const getUpcomingTests = asyncHandler(async (req, res) => {
    const candidateId = req.user._id; // Mongoose automatically converts string to ObjectId
    const now = new Date();

    const upcoming = await Test.find({
        participants: {
            $elemMatch: {
                candidateId,
                status: { $ne: 'submitted' }
            }
        },
        scheduledDate: { $gt: now },
        isActive: true
    })
        .populate('companyId', 'name logo')
        .populate('associatedJobs', 'title')
        .select('testName description scheduledDate endDate duration instructions')
        .lean();

    res.json({ success: true, data: upcoming });
});


const getLiveTests = asyncHandler(async (req, res) => {
    const candidateId = req.user._id;
    const now = new Date();

    // Live tests: scheduledDate has passed (started) but endDate hasn't passed yet
    // Use $elemMatch to ensure both candidateId and status conditions apply to the same participant
    const live = await Test.find({
        participants: {
            $elemMatch: {
                candidateId: candidateId,
                status: { $ne: 'submitted' }
            }
        },
        scheduledDate: { $lte: now },
        endDate: { $gte: now },
        isActive: true
    })
        .populate('companyId', 'name logo')
        .populate('associatedJobs', 'title')
        .select('testName description duration endDate instructions')
        .lean();

    res.json({ success: true, data: live });
});


const getTestHistory = asyncHandler(async (req, res) => {
    const candidateId = req.user._id;

    const history = await Test.find({
        'participants.candidateId': candidateId,
        'participants.status': { $in: ['submitted', 'evaluated'] }
    })
        .populate('companyId', 'name logo')
        .populate('associatedJobs', 'title')
        .select('testName scheduledDate endDate participants.$')
        .lean();

    res.json({ success: true, data: history });
});

const getTestDetails = asyncHandler(async (req, res) => {
    const { testId } = req.params;
    const candidateId = req.user._id; // Use _id directly instead of deprecated constructor

    const test = await Test.findOne({
        _id: testId,
        'participants.candidateId': candidateId,
        isActive: true
    })
        .populate('companyId', 'name logo')
        .populate('associatedJobs', 'title');

    if (!test) {
        res.status(404);
        throw new Error('Test not found or access denied.');
    }

    const participant = test.participants.find(
        p => p.candidateId.toString() === candidateId.toString()
    );

    const questions = test.questions.map(q => {
        const clone = q.toObject ? q.toObject() : { ...q };
        if (participant.status !== 'evaluated') {
            delete clone.isCorrect;
            delete clone.pointsEarned;
        }
        return clone;
    });

    res.json({
        success: true,
        data: {
            testName: test.testName,
            description: test.description,
            duration: test.duration,
            scheduledDate: test.scheduledDate,
            endDate: test.endDate,
            instructions: test.instructions,
            questions,
            participantStatus: participant.status,
            allowedAttempts: test.allowedAttempts,
            randomizeQuestions: test.randomizeQuestions,
            showResults: test.showResults
        }
    });
});

const startTest = asyncHandler(async (req, res) => {
    const { testId } = req.params;
    const candidateId = req.user._id;
    const now = new Date();

    const test = await Test.findOne({
        _id: testId,
        'participants.candidateId': candidateId,
        isActive: true,
        scheduledDate: { $lte: now },
        endDate: { $gte: now }
    });

    if (!test) {
        res.status(404);
        throw new Error('Test not found, not active, or time window passed.');
    }

    const participant = test.participants.find(p =>
        p.candidateId.toString() === candidateId.toString()
    );

    if (!participant) {
        res.status(403);
        throw new Error('You are not assigned to this test.');
    }

    if (participant.status === 'submitted') {
        res.status(409);
        throw new Error('Test already submitted.');
    }

    // If already started → resume
    if (participant.status === 'started') {
        return res.json({
            success: true,
            message: 'Resuming test.',
            attemptId: participant.attemptId,
            timeLeft: Math.max(0, test.duration * 60 * 1000 - (now - participant.startedAt))
        });
    }

    // Start fresh
    const attemptId = uuid();
    participant.status = 'started';
    participant.startedAt = now;
    participant.attemptId = attemptId;
    await test.save();

    res.json({
        success: true,
        attemptId,
        timeLeft: test.duration * 60 * 1000 // ms
    });
});


const submitTest = asyncHandler(async (req, res) => {
    const { testId } = req.params;
    const candidateId = req.user._id;
    const { answers, tabSwitchCount = 0, suspiciousActivity = false } = req.body;

    const test = await Test.findOne({
        _id: testId,
        'participants.candidateId': candidateId,
        // 'participants.attemptId': attemptId,
        isActive: true
    });

    if (!test) {
        res.status(404);
        throw new Error('Test or attempt not found.');
    }

    const participant = test.participants.find(p =>
        p.candidateId.toString() === candidateId.toString()
    );

    if (participant.status === 'submitted') {
        res.status(409);
        throw new Error('Test already submitted.');
    }

    // Evaluate answers
    let score = 0;
    const evaluatedAnswers = answers.map(a => {
        const q = test.questions.id(a.questionId);
        if (!q) return null;

        const isCorrect = a.answer === q.correctOption; // adapt if coding
        const pointsEarned = isCorrect ? q.points : 0;
        score += pointsEarned;

        return {
            questionId: a.questionId,
            answer: a.answer,
            isCorrect,
            pointsEarned
        };
    }).filter(Boolean);

    participant.status = 'submitted';
    participant.submittedAt = new Date();
    participant.answers = evaluatedAnswers;
    participant.score = score;
    participant.totalScore = test.totalPoints;
    participant.percentage = Number(((score / test.totalPoints) * 100).toFixed(2));
    participant.tabSwitchCount = tabSwitchCount;
    participant.suspiciousActivity = suspiciousActivity;

    await test.save();

    // Optional: Persist to Result collection for faster querying
    await Result.create({
        candidateId,
        testId,
        score,
        totalScore: test.totalPoints,
        percentage: participant.percentage,
        feedback: '',
        evaluatedBy: null,
        answers: evaluatedAnswers
    });

    res.json({
        success: true,
        message: 'Test submitted successfully.',
        score: participant.score,
        totalScore: participant.totalScore,
        percentage: participant.percentage
    });
});

module.exports = {
    getUpcomingTests,
    getLiveTests,
    getTestHistory,
    getTestDetails,
    startTest,
    submitTest
};