# Frontend Excel Upload Fix Guide

## 🚨 Issue
The error "MulterError: Unexpected field" occurs when the frontend sends a file with a field name that doesn't match what the backend expects.

## ✅ Solution

The backend now accepts Excel files with **any field name**, but here are the recommended approaches:

### **Option 1: Use Correct Field Name (Recommended)**

Make sure your frontend uses the field name `"excelFile"`:

```javascript
// ✅ Correct way
const formData = new FormData();
formData.append('excelFile', selectedFile);

// ❌ Wrong way
formData.append('file', selectedFile);        // Wrong field name
formData.append('upload', selectedFile);      // Wrong field name
formData.append('document', selectedFile);    // Wrong field name
```

### **Option 2: Any Field Name (Now Supported)**

After the backend update, you can use any field name:

```javascript
// ✅ All of these now work
formData.append('excelFile', selectedFile);   // Recommended
formData.append('file', selectedFile);        // Works
formData.append('upload', selectedFile);      // Works
formData.append('document', selectedFile);    // Works
```

## 📝 Complete Frontend Examples

### **React with Axios**

```jsx
import React, { useState } from 'react';
import axios from 'axios';

const ExcelUpload = () => {
  const [file, setFile] = useState(null);
  const [uploading, setUploading] = useState(false);

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
  };

  const handleUpload = async () => {
    if (!file) {
      alert('Please select a file');
      return;
    }

    setUploading(true);
    
    const formData = new FormData();
    formData.append('excelFile', file); // Use 'excelFile' field name
    
    try {
      const token = localStorage.getItem('authToken');
      
      const response = await axios.post('/api/questions/upload-excel', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`
        }
      });
      
      console.log('Upload successful:', response.data);
      alert(`Successfully uploaded ${response.data.summary.questionsInserted} questions!`);
      
    } catch (error) {
      console.error('Upload error:', error);
      alert('Upload failed: ' + (error.response?.data?.error || error.message));
    } finally {
      setUploading(false);
    }
  };

  return (
    <div>
      <input 
        type="file" 
        accept=".xlsx,.xls" 
        onChange={handleFileChange}
        disabled={uploading}
      />
      <button 
        onClick={handleUpload} 
        disabled={!file || uploading}
      >
        {uploading ? 'Uploading...' : 'Upload Excel'}
      </button>
    </div>
  );
};

export default ExcelUpload;
```

### **Vanilla JavaScript**

```javascript
// HTML
// <input type="file" id="fileInput" accept=".xlsx,.xls">
// <button onclick="uploadFile()">Upload</button>

async function uploadFile() {
  const fileInput = document.getElementById('fileInput');
  const file = fileInput.files[0];
  
  if (!file) {
    alert('Please select a file');
    return;
  }
  
  const formData = new FormData();
  formData.append('excelFile', file); // Use 'excelFile' field name
  
  try {
    const token = localStorage.getItem('authToken');
    
    const response = await fetch('/api/questions/upload-excel', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
        // Don't set Content-Type for FormData, browser will set it automatically
      },
      body: formData
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('Upload successful:', result);
      alert(`Successfully uploaded ${result.summary.questionsInserted} questions!`);
    } else {
      throw new Error(result.error || 'Upload failed');
    }
    
  } catch (error) {
    console.error('Upload error:', error);
    alert('Upload failed: ' + error.message);
  }
}
```

### **Vue.js**

```vue
<template>
  <div>
    <input 
      type="file" 
      @change="handleFileChange"
      accept=".xlsx,.xls"
      :disabled="uploading"
    />
    <button 
      @click="uploadFile" 
      :disabled="!file || uploading"
    >
      {{ uploading ? 'Uploading...' : 'Upload Excel' }}
    </button>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  data() {
    return {
      file: null,
      uploading: false
    };
  },
  methods: {
    handleFileChange(event) {
      this.file = event.target.files[0];
    },
    
    async uploadFile() {
      if (!this.file) {
        alert('Please select a file');
        return;
      }
      
      this.uploading = true;
      
      const formData = new FormData();
      formData.append('excelFile', this.file); // Use 'excelFile' field name
      
      try {
        const token = localStorage.getItem('authToken');
        
        const response = await axios.post('/api/questions/upload-excel', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${token}`
          }
        });
        
        console.log('Upload successful:', response.data);
        alert(`Successfully uploaded ${response.data.summary.questionsInserted} questions!`);
        
      } catch (error) {
        console.error('Upload error:', error);
        alert('Upload failed: ' + (error.response?.data?.error || error.message));
      } finally {
        this.uploading = false;
      }
    }
  }
};
</script>
```

## 🔍 Debugging Tips

### **1. Check Network Tab**
In browser DevTools > Network tab, look at the request:
- **Content-Type** should be `multipart/form-data`
- **Request payload** should show your file with the correct field name

### **2. Check Field Name**
Make sure the FormData field name matches:
```javascript
// Check what you're sending
const formData = new FormData();
formData.append('excelFile', file);

// Debug: Log the FormData contents
for (let [key, value] of formData.entries()) {
  console.log(key, value);
}
```

### **3. Check File Type**
Ensure you're sending Excel files:
```javascript
const allowedTypes = [
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'application/vnd.ms-excel' // .xls
];

if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
  alert('Please select an Excel file');
  return;
}
```

### **4. Check Authorization**
Make sure you're sending the correct auth token:
```javascript
const token = localStorage.getItem('authToken');
if (!token) {
  alert('Please login first');
  return;
}
```

## 🚀 Test the Fix

1. **Update your frontend** to use one of the examples above
2. **Select an Excel file** (.xlsx or .xls)
3. **Upload the file** - it should now work without the "Unexpected field" error
4. **Check the response** for upload results

## 📋 Expected Response

```json
{
  "success": true,
  "message": "Successfully processed Excel file",
  "summary": {
    "totalRowsProcessed": 15,
    "questionsInserted": 13,
    "duplicatesSkipped": 1,
    "validationErrors": 1
  },
  "questions": [...]
}
```

The backend has been updated to be more flexible with field names, so your upload should work now! 🎉
