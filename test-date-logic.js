const dayjs = require('dayjs');

// Test data similar to what you provided
const testData = {
    "_id": "688071c3ea424f848f401d5c",
    "companyId": "687e05cfe5a439d24aa55e0d",
    "testName": "Backend development round 2",
    "description": "skill test description",
    "scheduledDate": "2025-10-21T20:53:00.000Z",
    "endDate": "2025-10-22T02:59:00.000Z",
    "isActive": true,
    "participants": [
        {
            "candidateId": "687b8de029bed4831477d69b",
            "status": "assigned",
            "_id": "688071cfea424f848f401d8e"
        }
    ]
};

// Current date for testing
const now = new Date();
console.log('Current Date:', now.toISOString());

// Test dates from your example
const scheduledDate = new Date(testData.scheduledDate);
const endDate = new Date(testData.endDate);

console.log('Scheduled Date:', scheduledDate.toISOString());
console.log('End Date:', endDate.toISOString());

// Test the logic
console.log('\n--- Testing Current Logic ---');
console.log('scheduledDate > now:', scheduledDate > now, '(Should be true for upcoming)');
console.log('scheduledDate <= now:', scheduledDate <= now, '(Should be true for live)');
console.log('endDate >= now:', endDate >= now, '(Should be true for live)');

// Test different scenarios
console.log('\n--- Testing Different Scenarios ---');

// Scenario 1: Test scheduled for tomorrow, ends day after tomorrow (should be upcoming)
const tomorrow = new Date();
tomorrow.setDate(tomorrow.getDate() + 1);
const dayAfterTomorrow = new Date();
dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

console.log('\nScenario 1: Scheduled tomorrow, ends day after tomorrow');
console.log('Scheduled:', tomorrow.toISOString());
console.log('End:', dayAfterTomorrow.toISOString());
console.log('Is Upcoming (scheduled > now):', tomorrow > now);
console.log('Is Live (scheduled <= now && end >= now):', tomorrow <= now && dayAfterTomorrow >= now);

// Scenario 2: Test scheduled yesterday, ends tomorrow (should be live)
const yesterday = new Date();
yesterday.setDate(yesterday.getDate() - 1);
const tomorrowForLive = new Date();
tomorrowForLive.setDate(tomorrowForLive.getDate() + 1);

console.log('\nScenario 2: Scheduled yesterday, ends tomorrow');
console.log('Scheduled:', yesterday.toISOString());
console.log('End:', tomorrowForLive.toISOString());
console.log('Is Upcoming (scheduled > now):', yesterday > now);
console.log('Is Live (scheduled <= now && end >= now):', yesterday <= now && tomorrowForLive >= now);

// Scenario 3: Test scheduled in past, ended in past (should be neither)
const twoDaysAgo = new Date();
twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
const oneDayAgo = new Date();
oneDayAgo.setDate(oneDayAgo.getDate() - 1);

console.log('\nScenario 3: Scheduled two days ago, ended yesterday');
console.log('Scheduled:', twoDaysAgo.toISOString());
console.log('End:', oneDayAgo.toISOString());
console.log('Is Upcoming (scheduled > now):', twoDaysAgo > now);
console.log('Is Live (scheduled <= now && end >= now):', twoDaysAgo <= now && oneDayAgo >= now);

// Test with the actual data from your example
console.log('\n--- Testing Your Actual Data ---');
console.log('Your test scheduled date:', scheduledDate.toISOString());
console.log('Your test end date:', endDate.toISOString());
console.log('Current time:', now.toISOString());

// Check if your test should be upcoming or live
const isUpcoming = scheduledDate > now;
const isLive = scheduledDate <= now && endDate >= now;

console.log('Should be Upcoming:', isUpcoming);
console.log('Should be Live:', isLive);

if (isUpcoming) {
    console.log('✅ This test should appear in UPCOMING tests');
} else if (isLive) {
    console.log('✅ This test should appear in LIVE tests');
} else {
    console.log('❌ This test should NOT appear in either (expired)');
}
